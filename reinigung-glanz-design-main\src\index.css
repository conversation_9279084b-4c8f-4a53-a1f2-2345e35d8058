
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired design system variables */
@layer base {
  :root {
    /* Apple-Inspired Design System - SUZ Reinigung */

    /* Primary Brand Colors */
    --suz-blue-primary: #007AFF;
    --suz-blue-secondary: #5AC8FA;
    --suz-blue-tertiary: #34C759;

    /* Neutral Foundation */
    --suz-gray-50: #F9FAFB;
    --suz-gray-100: #F3F4F6;
    --suz-gray-200: #E5E7EB;
    --suz-gray-300: #D1D5DB;
    --suz-gray-400: #9CA3AF;
    --suz-gray-500: #6B7280;
    --suz-gray-600: #4B5563;
    --suz-gray-700: #374151;
    --suz-gray-800: #1F2937;
    --suz-gray-900: #111827;

    /* Semantic Colors */
    --suz-success: #34C759;
    --suz-warning: #FF9500;
    --suz-error: #FF3B30;
    --suz-info: #007AFF;

    /* Surface Colors */
    --suz-surface-primary: #FFFFFF;
    --suz-surface-secondary: #F9FAFB;
    --suz-surface-tertiary: #F3F4F6;
    --suz-surface-glass: rgba(255, 255, 255, 0.8);

    /* Typography Scale */
    --text-display-xl: 4.5rem;    /* 72px - Hero headlines */
    --text-display-lg: 3.75rem;   /* 60px - Section headers */
    --text-display-md: 3rem;      /* 48px - Page titles */
    --text-display-sm: 2.25rem;   /* 36px - Card titles */

    --text-heading-xl: 1.875rem;  /* 30px - H1 */
    --text-heading-lg: 1.5rem;    /* 24px - H2 */
    --text-heading-md: 1.25rem;   /* 20px - H3 */
    --text-heading-sm: 1.125rem;  /* 18px - H4 */

    --text-body-xl: 1.125rem;     /* 18px - Large body */
    --text-body-lg: 1rem;         /* 16px - Default body */
    --text-body-md: 0.875rem;     /* 14px - Small body */
    --text-body-sm: 0.75rem;      /* 12px - Caption */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing Scale (8px base unit) */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */
    --space-32: 8rem;     /* 128px */

    /* Layout Spacing */
    --section-padding-sm: var(--space-16);
    --section-padding-md: var(--space-20);
    --section-padding-lg: var(--space-24);
    --section-padding-xl: var(--space-32);

    --component-padding-sm: var(--space-4);
    --component-padding-md: var(--space-6);
    --component-padding-lg: var(--space-8);

    /* Border Radius System */
    --radius-none: 0;
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-3xl: 2rem;      /* 32px */
    --radius-full: 9999px;

    /* Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-blue: 0 10px 25px -5px rgba(0, 122, 255, 0.15);
    --shadow-green: 0 10px 25px -5px rgba(52, 199, 89, 0.15);

    /* Animation Timing */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
    --duration-slower: 500ms;

    /* Legacy Shadcn/UI Variables (for compatibility) */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    /* Apple-Inspired Dark Theme - SUZ Reinigung */

    /* Primary Brand Colors - Dark Mode */
    --suz-blue-primary: #0A84FF;
    --suz-blue-secondary: #64D2FF;
    --suz-blue-tertiary: #30D158;

    /* Dark Neutral Foundation */
    --suz-gray-50: #1C1C1E;
    --suz-gray-100: #2C2C2E;
    --suz-gray-200: #3A3A3C;
    --suz-gray-300: #48484A;
    --suz-gray-400: #636366;
    --suz-gray-500: #8E8E93;
    --suz-gray-600: #AEAEB2;
    --suz-gray-700: #C7C7CC;
    --suz-gray-800: #D1D1D6;
    --suz-gray-900: #F2F2F7;

    /* Dark Semantic Colors */
    --suz-success: #30D158;
    --suz-warning: #FF9F0A;
    --suz-error: #FF453A;
    --suz-info: #0A84FF;

    /* Dark Surface Colors */
    --suz-surface-primary: #000000;
    --suz-surface-secondary: #1C1C1E;
    --suz-surface-tertiary: #2C2C2E;
    --suz-surface-glass: rgba(28, 28, 30, 0.8);

    /* Legacy Shadcn/UI Variables (for compatibility) */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    font-weight: 400;
  }

  body.force-apple-design {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Apple-inspired component styles */
@layer components {

/* Premium Background Gradient - Force Priority */
.bg-premium-gradient {
  background: linear-gradient(135deg,
    #f8fafc 0%,
    #e2e8f0 20%,
    #f1f5f9 40%,
    #e0f2fe 60%,
    #f0f9ff 80%,
    #fafbfc 100%) !important;
  min-height: 100vh !important;
  position: relative;
}

.bg-premium-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* Force Apple Design System Priority */
body.force-apple-design,
.force-apple-design {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.force-apple-design .bg-premium-gradient {
  background: linear-gradient(135deg,
    #f8fafc 0%,
    #e2e8f0 25%,
    #f1f5f9 50%,
    #e0f2fe 75%,
    #f0f9ff 100%) !important;
}

/* Enhanced Glass Morphism Effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
}

.glass-morphism-premium {
  background: rgba(255, 255, 255, 0.25) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  box-shadow:
    0 20px 40px -10px rgba(59, 130, 246, 0.1),
    0 10px 25px -5px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Enhanced Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
}

.gradient-text-animated {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6, #3b82f6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 300% 300% !important;
  animation: gradient-shift 6s ease-in-out infinite !important;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Pulse Glow Effect */
.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(59, 130, 246, 0.5), 0 0 40px rgba(6, 182, 212, 0.3);
  }
}

/* Logo Glow Effect */
.logo-glow:hover {
  box-shadow: 
    0 0 30px rgba(59, 130, 246, 0.4),
    0 0 60px rgba(6, 182, 212, 0.2),
    0 20px 40px -10px rgba(59, 130, 246, 0.15);
}

/* Premium Button Effects */
.premium-button {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 10px 30px -5px rgba(0, 0, 0, 0.25),
    0 0 20px rgba(255, 255, 255, 0.2) inset;
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button-3d {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 15px 35px -5px rgba(0, 0, 0, 0.2),
    0 5px 15px -3px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(255, 255, 255, 0.3) inset;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-3d:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.25),
    0 8px 20px -3px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(255, 255, 255, 0.4) inset;
}

/* Enhanced Service Card Hover Effect */
.service-card-premium {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.08), 
    rgba(6, 182, 212, 0.08),
    rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: inherit;
}

.service-card-premium:hover::before {
  opacity: 1;
}

.service-card-premium:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 25px 50px -10px rgba(59, 130, 246, 0.15),
    0 10px 30px -5px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Icon Badge Style */
.icon-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-badge:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 30px rgba(59, 130, 246, 0.2),
    0 6px 15px rgba(6, 182, 212, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Enhanced Icon Badge Style for Better Visibility */
.icon-badge-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15),
    rgba(6, 182, 212, 0.12),
    rgba(255, 255, 255, 0.8));
  border-radius: 50%;
  border: 2px solid rgba(59, 130, 246, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.25),
    0 6px 18px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.9),
    inset 0 -2px 0 rgba(59, 130, 246, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.icon-badge-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.08));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.icon-badge-enhanced:hover::before {
  opacity: 1;
}

.icon-badge-enhanced:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 20px 45px rgba(59, 130, 246, 0.35),
    0 10px 25px rgba(6, 182, 212, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 1),
    inset 0 -2px 0 rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Team Card Effects */
.team-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 40px -10px rgba(59, 130, 246, 0.12),
    0 8px 25px -5px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Leadership Card Enhanced Styling */
.leadership-card {
  position: relative;
  overflow: hidden;
}

.leadership-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.03));
  border-radius: inherit;
  pointer-events: none;
}

.leadership-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -10px rgba(59, 130, 246, 0.2),
    0 12px 30px -5px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Enhanced Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.floating-element {
  animation: float 8s ease-in-out infinite;
}

/* Enhanced Fade In Animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Premium Micro-Interactions */
@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes gentle-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes soft-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4), 0 0 40px rgba(6, 182, 212, 0.2);
  }
}

/* Company Showcase Infinite Scroll Animation */
@keyframes scroll-right {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll-right {
  animation: scroll-right 60s linear infinite;
}

/* Company Showcase Styles */
.suz-company-showcase {
  position: relative;
  background: linear-gradient(135deg,
    rgba(249, 250, 251, 0.8) 0%,
    rgba(243, 244, 246, 0.9) 50%,
    rgba(249, 250, 251, 0.8) 100%
  );
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: var(--space-16) 0;
  margin-top: var(--space-12);
  margin-bottom: var(--space-12);
}

.suz-company-scroll {
  /* Optimize for smooth animation */
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
  width: max-content;
}

.suz-company-card {
  /* Prevent layout shifts during animation */
  contain: layout style paint;
  transform: translateZ(0);
  min-width: 280px;
  max-width: 320px;
}

.suz-company-card-content {
  padding: var(--component-padding-md);
}

/* Pause animation on hover for better UX */
.suz-company-showcase:hover .animate-scroll-right {
  animation-play-state: paused;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-scroll-right {
    animation: none;
  }

  .suz-company-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
  }
}

/* Company Showcase Responsive Design */
@media (max-width: 768px) {
  .suz-company-showcase {
    padding: var(--space-12) 0;
    margin-top: var(--space-8);
    margin-bottom: var(--space-8);
  }

  .suz-company-card {
    min-width: 240px;
    max-width: 280px;
  }

  .animate-scroll-right {
    animation-duration: 45s; /* Faster on mobile for better UX */
  }

  /* Adjust fade gradients for mobile */
  .suz-company-showcase .absolute.w-20 {
    width: 3rem; /* Smaller fade areas on mobile */
  }

  /* Improve touch interaction on mobile */
  .suz-company-showcase .suz-card-glass:hover {
    transform: none; /* Disable hover effects on mobile */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .suz-company-card {
    min-width: 260px;
    max-width: 300px;
  }

  .animate-scroll-right {
    animation-duration: 50s; /* Medium speed for tablets */
  }
}

/* Enhanced glass morphism for company cards */
.suz-company-showcase .suz-card-glass {
  background: rgba(255, 255, 255, 0.85) !important;
  -webkit-backdrop-filter: blur(16px) !important;
  backdrop-filter: blur(16px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

.suz-company-showcase .suz-card-glass:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

/* Enhanced Button Interactions */
.premium-button-enhanced {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.premium-button-enhanced:hover::before {
  left: 100%;
}

.premium-button-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
}

/* Smooth Card Hover Effects */
.card-hover-enhanced {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-hover-enhanced:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.15),
    0 10px 30px -5px rgba(59, 130, 246, 0.1);
}

/* Text Reveal Animation */
@keyframes text-reveal {
  from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.text-reveal {
  animation: text-reveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Keep essential focus transitions */
  *:focus {
    transition-duration: 0.1s !important;
  }
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(6, 182, 212, 0.4));
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(6, 182, 212, 0.6));
}

/* Mobile Responsiveness Enhancements */
@media (max-width: 768px) {
  .glass-morphism-premium {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  .logo-glow img {
    min-width: 40px !important;
    min-height: 40px !important;
  }

  .service-card-premium {
    margin-bottom: 1rem;
  }

  .team-card {
    margin-bottom: 1rem;
  }

  /* Enhanced mobile typography */
  .suz-text-display-xl {
    font-size: 2.5rem !important;
    line-height: 1.1 !important;
  }

  .suz-text-display-lg {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }

  .suz-text-heading-xl {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  /* Enhanced mobile navigation improvements */
  nav[role="navigation"] {
    top: 1rem !important;
    /* Ensure perfect centering on mobile */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 1rem) !important;
  }

  nav .suz-card-glass {
    padding: 0.5rem 0.75rem !important;
    margin: 0 auto !important;
  }

  nav .flex {
    flex-wrap: nowrap !important;
    gap: 0.25rem !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .suz-nav-link {
    font-size: 0.8rem !important;
    padding: 0.4rem 0.6rem !important;
    white-space: nowrap !important;
    text-align: center !important;
  }

  /* Mobile button improvements */
  .suz-btn-primary {
    padding: var(--space-4) var(--space-8) !important;
    font-size: 1rem !important;
    min-height: 48px; /* Touch target size */
  }

  /* Mobile spacing adjustments */
  section {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Tablet responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-text-display-xl {
    font-size: 3.5rem;
  }

  .grid.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .grid.lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Performance Optimization Classes */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
  opacity: 1;
}

.image-optimized {
  width: 100%;
  height: auto;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Preload critical resources */
.critical-resource {
  font-display: swap;
}

/* Apple-Inspired Component Classes - High Priority */

/* Button System */
.suz-btn-primary {
  background: var(--suz-blue-primary) !important;
  color: white !important;
  padding: var(--space-3) var(--space-6) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--text-body-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--duration-normal) var(--ease-out-cubic) !important;
  border: none !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-decoration: none !important;
}

.suz-btn-primary:hover {
  background: #0056CC !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.suz-btn-secondary {
  background: var(--suz-surface-primary);
  color: var(--suz-blue-primary);
  border: 1px solid var(--suz-gray-200);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.suz-btn-secondary:hover {
  background: var(--suz-gray-50);
  border-color: var(--suz-blue-primary);
  transform: translateY(-1px);
}

/* Card System */
.suz-card-primary {
  background: var(--suz-surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  border: 1px solid var(--suz-gray-100);
  transition: all var(--duration-normal) var(--ease-out-cubic);
}

.suz-card-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.suz-card-glass {
  background: var(--suz-surface-glass) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--space-6) !important;
}

/* Navigation System */
.suz-nav-primary {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--suz-gray-100);
  padding: var(--space-4) 0;
}

/* Enhanced Navigation Centering */
nav[role="navigation"] {
  /* Ensure perfect horizontal centering */
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  /* Prevent layout shifts */
  will-change: transform;
  /* Ensure proper stacking */
  z-index: 50;
  /* Ensure content doesn't overflow */
  box-sizing: border-box;
  /* Center the navigation container */
  display: block !important;
  text-align: center;
}

/* Ensure the glass container is centered within the nav */
nav[role="navigation"] > div {
  margin: 0 auto;
  display: inline-block;
}

/* Additional centering support for all screen sizes */
@media (max-width: 480px) {
  nav[role="navigation"] {
    /* Extra small screens - ensure perfect centering */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 1rem) !important;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  nav[role="navigation"] {
    /* Small to medium screens */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: calc(100vw - 2rem) !important;
  }
}

@media (min-width: 769px) {
  nav[role="navigation"] {
    /* Large screens and up */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: none !important;
  }
}



.suz-nav-link {
  color: var(--suz-gray-600);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  text-decoration: none;
  cursor: pointer;
  border: none;
  background: none;
}

.suz-nav-link:hover {
  color: var(--suz-blue-primary);
  background: var(--suz-gray-50);
  transform: scale(1.05);
}

/* Typography Classes */
.suz-text-display-xl {
  font-size: var(--text-display-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-display-lg {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-heading-xl {
  font-size: var(--text-heading-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
}

.suz-text-heading-lg {
  font-size: var(--text-heading-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-normal) !important;
}

.suz-text-body-lg {
  font-size: var(--text-body-lg);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
}

/* Enhanced Section Title Class */
.suz-section-title {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Focus States */
.suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 300;
  letter-spacing: -0.025em;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* Core Web Vitals Optimizations */

/* Optimize Largest Contentful Paint (LCP) */
.optimize-lcp {
  font-display: swap;
  contain: layout style paint;
  will-change: auto;
}

/* Reduce Cumulative Layout Shift (CLS) */
.optimize-cls {
  aspect-ratio: 16/9;
  width: 100%;
  height: auto;
}

/* Improve First Input Delay (FID) */
.optimize-fid {
  touch-action: manipulation;
  -webkit-user-select: none;
  user-select: none;
}

/* Critical above-the-fold content */
.critical-content {
  contain: layout style paint;
  will-change: auto;
}

/* Non-critical content that can be deferred */
.defer-content {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Optimize animations for performance */
.performance-animation {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize images for Core Web Vitals */
.optimize-image {
  aspect-ratio: attr(width) / attr(height);
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Reduce layout thrashing */
.stable-layout {
  contain: layout;
  min-height: 200px;
}

/* Critical CSS for above-the-fold content */
.hero-critical {
  min-height: 100vh;
  contain: layout style paint;
  will-change: auto;
}

.hero-critical h1 {
  font-display: swap;
  contain: layout style;
}

/* Optimize button interactions */
.btn-optimized {
  touch-action: manipulation;
  will-change: transform;
  backface-visibility: hidden;
}

.btn-optimized:hover,
.btn-optimized:focus {
  transform: translateZ(0) scale(1.05);
}

/* Preload critical fonts */
@font-face {
  font-family: 'Inter';
  font-display: swap;
  font-weight: 100 900;
}

/* Optimize scrolling performance */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Optimize focus states for accessibility and performance */
.focus-optimized:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Screen Reader Support */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-morphism,
  .glass-morphism-premium {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }

  .gradient-text,
  .gradient-text-animated {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    color: #000;
  }
}

/* Focus Management */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--suz-blue-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Keyboard Navigation Enhancement */
.keyboard-nav *:focus {
  outline: 3px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* Color Blind Friendly Patterns */
.pattern-support {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.1) 2px,
    rgba(0, 0, 0, 0.1) 4px
  );
}

/* Dark Theme Specific Styles */

/* Dark Premium Background Gradient */
.dark .bg-premium-gradient {
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
  min-height: 100vh !important;
}

/* Dark Glass Morphism */
.dark .glass-morphism-premium {
  background: rgba(28, 28, 30, 0.4) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  box-shadow:
    0 20px 40px -10px rgba(10, 132, 255, 0.15),
    0 10px 25px -5px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Dark Gradient Text */
.dark .gradient-text-animated {
  background: linear-gradient(135deg, #0A84FF, #64D2FF, #30D158, #FF9F0A, #0A84FF) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 300% 300% !important;
  animation: gradient-shift 6s ease-in-out infinite !important;
}

/* Dark Service Cards */
.dark .service-card-premium {
  background: var(--suz-surface-secondary);
  border: 1px solid var(--suz-gray-200);
}

.dark .service-card-premium::before {
  background: linear-gradient(135deg,
    rgba(10, 132, 255, 0.1),
    rgba(100, 210, 255, 0.08),
    rgba(48, 209, 88, 0.06));
}

.dark .service-card-premium:hover {
  box-shadow:
    0 25px 50px -10px rgba(10, 132, 255, 0.2),
    0 10px 30px -5px rgba(100, 210, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dark Navigation */
.dark .suz-nav-primary {
  background: rgba(28, 28, 30, 0.95);
  border-bottom: 1px solid var(--suz-gray-200);
}

.dark .suz-nav-link {
  color: var(--suz-gray-600);
}

.dark .suz-nav-link:hover {
  color: var(--suz-blue-primary);
  background: var(--suz-gray-100);
}

/* Dark Buttons */
.dark .suz-btn-primary {
  background: var(--suz-blue-primary) !important;
  box-shadow:
    0 10px 30px -5px rgba(10, 132, 255, 0.3),
    0 4px 15px -2px rgba(10, 132, 255, 0.2) !important;
}

.dark .suz-btn-primary:hover {
  background: #0056CC !important;
  box-shadow:
    0 15px 35px -5px rgba(10, 132, 255, 0.4),
    0 8px 20px -3px rgba(10, 132, 255, 0.3) !important;
}

.dark .suz-btn-secondary {
  background: var(--suz-surface-secondary);
  color: var(--suz-blue-primary);
  border: 1px solid var(--suz-gray-200);
}

.dark .suz-btn-secondary:hover {
  background: var(--suz-gray-100);
  border-color: var(--suz-blue-primary);
}

/* Dark Cards */
.dark .suz-card-primary {
  background: var(--suz-surface-secondary);
  border: 1px solid var(--suz-gray-200);
  box-shadow:
    0 4px 15px -2px rgba(0, 0, 0, 0.3),
    0 2px 8px -1px rgba(0, 0, 0, 0.2);
}

.dark .suz-card-primary:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.4),
    0 4px 15px -2px rgba(0, 0, 0, 0.3);
}

/* Dark Team Cards */
.dark .team-card:hover {
  box-shadow:
    0 20px 40px -10px rgba(10, 132, 255, 0.2),
    0 8px 25px -5px rgba(100, 210, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dark Scrollbar */
.dark ::-webkit-scrollbar-track {
  background: rgba(28, 28, 30, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.6), rgba(100, 210, 255, 0.6));
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.8), rgba(100, 210, 255, 0.8));
}

/* Dark Text Colors */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: var(--suz-gray-900);
}

.dark p, .dark span, .dark div {
  color: var(--suz-gray-700);
}

/* Dark Focus States */
.dark .suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* End of @layer components */
}

/* Additional utility overrides */
@layer utilities {
  .force-apple-design {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }
}
